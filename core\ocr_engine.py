"""
OCR核心引擎
"""

import os
import logging
import numpy as np
import cv2
from typing import List, Dict, Any, Optional, Callable, Union
from pathlib import Path
from PIL import Image


try:
    import paddleocr
    from paddleocr import PaddleOCR
    PADDLEOCR_AVAILABLE = True

    # 尝试导入PP-StructureV3
    try:
        from paddleocr import PPStructureV3
        PP_STRUCTURE_V3_AVAILABLE = True
    except ImportError:
        PP_STRUCTURE_V3_AVAILABLE = False
        PPStructureV3 = None

    # 尝试导入PP-DocTranslation
    try:
        from paddleocr import PPDocTranslation
        PP_DOC_TRANSLATION_AVAILABLE = True
    except ImportError:
        PP_DOC_TRANSLATION_AVAILABLE = False
        PPDocTranslation = None

except ImportError:
    PADDLEOCR_AVAILABLE = False
    PP_STRUCTURE_V3_AVAILABLE = False
    PP_DOC_TRANSLATION_AVAILABLE = False
    PPStructureV3 = None
    PPDocTranslation = None

from config.settings import OCRSettings
from config.models_config import ModelsConfig
from utils.image_utils import ImageUtils


class OCRResult:
    """OCR识别结果"""
    
    def __init__(self):
        self.text_blocks: List[Dict[str, Any]] = []
        self.tables: List[Dict[str, Any]] = []
        self.layout_info: Dict[str, Any] = {}
        self.confidence_scores: List[float] = []
        self.processing_time: float = 0.0
        self.page_number: int = 0
    
    def add_text_block(self, text: str, bbox: List[float], confidence: float):
        """添加文本块"""
        self.text_blocks.append({
            'text': text,
            'bbox': bbox,
            'confidence': confidence
        })
        self.confidence_scores.append(confidence)
    
    def add_table(self, table_data: Dict[str, Any]):
        """添加表格数据"""
        self.tables.append(table_data)
    
    def get_all_text(self) -> str:
        """获取所有识别的文本"""
        return '\n'.join([block['text'] for block in self.text_blocks])
    
    def get_average_confidence(self) -> float:
        """获取平均置信度"""
        if not self.confidence_scores:
            return 0.0
        return sum(self.confidence_scores) / len(self.confidence_scores)


class OCREngine:
    """OCR引擎"""
    
    def __init__(self, settings: OCRSettings, models_config: ModelsConfig):
        self.settings = settings
        self.models_config = models_config
        self.logger = logging.getLogger(__name__)

        # OCR实例
        self.ocr_general = None
        self.ocr_table = None
        self.ocr_layout = None
        self.pp_structure_v3 = None  # PP-StructureV3实例
        self.pp_doc_translation = None  # PP-DocTranslation实例

        # 初始化状态
        self.is_initialized = False
        self.current_mode = 'general'  # 'general', 'table', 'structure_v3', 或 'doc_translation'

        # 回调函数
        self.progress_callback = None
        self.page_processed_callback = None
        self.error_callback = None
    
    def initialize(self) -> bool:
        """初始化OCR引擎"""
        if not PADDLEOCR_AVAILABLE:
            if self.error_callback:
                self.error_callback("PaddleOCR未安装，请安装相关依赖")
            return False
        
        try:
            if self.progress_callback:
                self.progress_callback(10, "正在初始化OCR引擎...")

            # 获取模型路径
            ocr_model_paths = self.models_config.get_ocr_model_paths()
            table_model_paths = self.models_config.get_table_model_paths()

            # 初始化通用OCR - 使用兼容的参数
            ocr_kwargs = {
                'use_angle_cls': self.settings.use_angle_cls,
                'lang': self.settings.lang,
                'det_db_thresh': 0.3,  # 检测阈值，降低以提高检测敏感度
                'det_db_box_thresh': 0.6,  # 边界框阈值
                'rec_batch_num': 6  # 识别批次大小
            }

            # 注意：use_space_char在新版本PaddleOCR中已被移除，不再添加此参数

            # 添加自定义模型路径
            if ocr_model_paths:
                ocr_kwargs.update(ocr_model_paths)

            if self.progress_callback:
                self.progress_callback(30, "正在加载通用OCR模型...")
            self.ocr_general = PaddleOCR(**ocr_kwargs)

            # 初始化PP-DocTranslation（如果可用）
            if PP_DOC_TRANSLATION_AVAILABLE:
                if self.progress_callback:
                    self.progress_callback(60, "正在初始化PP-DocTranslation...")
                try:
                    self.pp_doc_translation = PPDocTranslation(
                        use_doc_orientation_classify=False,
                        use_doc_unwarping=False,
                        use_textline_orientation=True,
                        use_seal_recognition=True,
                        use_table_recognition=True,
                        use_formula_recognition=True,
                        use_chart_recognition=False,
                        use_region_detection=True,
                        device='cpu'  # 可以根据设置调整
                    )
                    self.logger.info("PP-DocTranslation初始化成功")
                except Exception as e:
                    self.logger.warning(f"PP-DocTranslation初始化失败: {e}")
                    self.pp_doc_translation = None

            # 初始化表格识别（如果需要且PP-DocTranslation不可用）
            if table_model_paths and not PP_DOC_TRANSLATION_AVAILABLE:
                if self.progress_callback:
                    self.progress_callback(70, "正在加载表格识别模型...")
                try:
                    from paddleocr import PPStructure
                    structure_kwargs = {}
                    structure_kwargs.update(table_model_paths)
                    self.ocr_table = PPStructure(**structure_kwargs)
                except ImportError:
                    self.logger.warning("PPStructure不可用，表格模式将使用通用OCR")

            # 初始化PP-StructureV3（如果可用）
            if PP_STRUCTURE_V3_AVAILABLE:
                if self.progress_callback:
                    self.progress_callback(80, "正在初始化PP-StructureV3...")

                try:
                    self.pp_structure_v3 = PPStructureV3(
                        use_doc_orientation_classify=False,
                        use_doc_unwarping=False,
                        use_textline_orientation=False,
                        use_seal_recognition=True,
                        use_table_recognition=True,
                        use_formula_recognition=True,
                        use_chart_recognition=False,
                        use_region_detection=True
                    )
                    self.logger.info("PP-StructureV3初始化成功")
                except Exception as e:
                    self.logger.warning(f"PP-StructureV3初始化失败: {e}")
                    self.pp_structure_v3 = None

            if self.progress_callback:
                self.progress_callback(100, "OCR引擎初始化完成")
            self.is_initialized = True
            return True
            
        except Exception as e:
            error_msg = f"OCR引擎初始化失败: {e}"
            self.logger.error(error_msg)
            if self.error_callback:
                self.error_callback(error_msg)
            return False
    
    def set_mode(self, mode: str):
        """设置处理模式"""
        if mode in ['general', 'table', 'structure_v3', 'doc_translation']:
            if mode == 'structure_v3' and not PP_STRUCTURE_V3_AVAILABLE:
                raise ValueError("PP-StructureV3不可用，请安装完整版PaddleOCR")
            if mode == 'doc_translation' and not PP_DOC_TRANSLATION_AVAILABLE:
                raise ValueError("PP-DocTranslation不可用，请安装完整版PaddleOCR")
            self.current_mode = mode
        else:
            raise ValueError(f"不支持的模式: {mode}")
    
    def process_image(self, image: Union[np.ndarray, Image.Image, str, Path], 
                     page_number: int = 0) -> OCRResult:
        """
        处理单张图像
        
        Args:
            image: 图像数据（numpy数组、PIL图像或文件路径）
            page_number: 页码
            
        Returns:
            OCR识别结果
        """
        if not self.is_initialized:
            raise RuntimeError("OCR引擎未初始化")
        
        result = OCRResult()
        result.page_number = page_number
        
        try:
            import time
            start_time = time.time()
            
            # 预处理图像
            processed_image = self._preprocess_image(image)
            
            if self.current_mode == 'general':
                result = self._process_general_ocr(processed_image, result)
            elif self.current_mode == 'table':
                result = self._process_table_ocr(processed_image, result)
            elif self.current_mode == 'structure_v3':
                result = self._process_structure_v3_ocr(processed_image, result)
            elif self.current_mode == 'doc_translation':
                result = self._process_doc_translation_ocr(processed_image, result)
            
            result.processing_time = time.time() - start_time
            
            # 调用页面处理完成回调
            if self.page_processed_callback:
                self.page_processed_callback(page_number, result)
            
            return result
            
        except Exception as e:
            error_msg = f"图像处理失败 (页面 {page_number}): {e}"
            self.logger.error(error_msg)
            if self.error_callback:
                self.error_callback(error_msg)
            raise
    
    def _preprocess_image(self, image: Union[np.ndarray, Image.Image, str, Path]) -> np.ndarray:
        """预处理图像"""
        if isinstance(image, (str, Path)):
            # 从文件路径加载
            image = ImageUtils.load_image(image)
        elif isinstance(image, Image.Image):
            # PIL图像转OpenCV
            image = ImageUtils.pil_to_cv2(image)
        elif isinstance(image, np.ndarray):
            # 已经是numpy数组
            pass
        else:
            raise ValueError(f"不支持的图像类型: {type(image)}")

        # 记录原始图像信息
        self.logger.debug(f"预处理图像尺寸: {image.shape}")
        self.logger.debug(f"图像数据类型: {image.dtype}")

        # 图像质量检查
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image

        mean_brightness = np.mean(gray)
        self.logger.debug(f"图像平均亮度: {mean_brightness}")

        # 如果图像过暗或过亮，进行预处理
        if mean_brightness < 50 or mean_brightness > 200:
            self.logger.info(f"图像亮度异常({mean_brightness})，应用增强处理")
            image = self._enhance_image_quality(image)

        return image

    def _enhance_image_quality(self, image: np.ndarray) -> np.ndarray:
        """增强图像质量以提高OCR识别率"""
        try:
            # 转换为灰度图进行分析
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()

            # 自适应直方图均衡化
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
            enhanced_gray = clahe.apply(gray)

            # 如果原图是彩色的，保持彩色格式
            if len(image.shape) == 3:
                # 转换到LAB色彩空间进行亮度增强
                lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
                lab[:,:,0] = enhanced_gray
                enhanced = cv2.cvtColor(lab, cv2.COLOR_LAB2BGR)
            else:
                enhanced = enhanced_gray

            self.logger.debug("应用了图像质量增强")
            return enhanced

        except Exception as e:
            self.logger.warning(f"图像增强失败，使用原图: {e}")
            return image

    def _process_general_ocr(self, image: np.ndarray, result: OCRResult) -> OCRResult:
        """处理通用OCR"""
        try:
            self.logger.debug("开始执行OCR识别...")

            # 执行OCR识别
            ocr_results = self.ocr_general.predict(image)

            # 详细记录OCR原始结果
            self.logger.debug(f"OCR原始结果类型: {type(ocr_results)}")
            self.logger.debug(f"OCR结果长度: {len(ocr_results) if ocr_results else 0}")

            if not ocr_results:
                self.logger.warning("OCR返回None结果")
                return result

            if not ocr_results[0]:
                self.logger.warning("OCR返回空列表结果")
                return result

            detected_lines = len(ocr_results[0])
            self.logger.info(f"检测到 {detected_lines} 个文本行")

            # 解析结果
            valid_blocks = 0
            for i, line in enumerate(ocr_results[0]):
                try:
                    if len(line) >= 2:
                        bbox = line[0]  # 边界框坐标
                        text_info = line[1]  # (文本, 置信度)

                        if isinstance(text_info, (list, tuple)) and len(text_info) >= 2:
                            text = text_info[0]
                            confidence = text_info[1]

                            # 过滤低置信度和空文本
                            if confidence > 0.1 and text.strip():
                                # 转换边界框格式
                                bbox_flat = [coord for point in bbox for coord in point]
                                result.add_text_block(text, bbox_flat, confidence)
                                valid_blocks += 1

                                # 记录前几个识别结果用于调试
                                if i < 3:
                                    self.logger.debug(f"文本块{i}: '{text}' (置信度: {confidence:.3f})")
                        else:
                            self.logger.debug(f"文本行{i}格式异常: {text_info}")
                    else:
                        self.logger.debug(f"文本行{i}长度不足: {len(line)}")

                except Exception as e:
                    self.logger.warning(f"解析文本行{i}失败: {e}")
                    continue

            self.logger.info(f"成功解析 {valid_blocks} 个有效文本块")

            # 如果没有识别到有效文本，尝试降低阈值重试
            if valid_blocks == 0 and detected_lines > 0:
                self.logger.info("未识别到有效文本，尝试降低置信度阈值重试...")
                return self._retry_with_lower_threshold(image, result)

            return result

        except Exception as e:
            self.logger.error(f"通用OCR处理失败: {e}")
            raise

    def _retry_with_lower_threshold(self, image: np.ndarray, result: OCRResult) -> OCRResult:
        """使用更低的置信度阈值重试OCR识别"""
        try:
            self.logger.info("使用降低的置信度阈值重试OCR识别...")

            # 重新创建OCR实例，使用更宽松的参数
            retry_kwargs = {
                'use_angle_cls': self.settings.use_angle_cls,
                'lang': self.settings.lang,
                'det_db_thresh': 0.1,  # 大幅降低检测阈值
                'det_db_box_thresh': 0.3,  # 降低边界框阈值
                'rec_batch_num': 6
            }

            # 临时创建新的OCR实例
            temp_ocr = PaddleOCR(**retry_kwargs)
            ocr_results = temp_ocr.predict(image)

            if ocr_results and ocr_results[0]:
                self.logger.info(f"重试检测到 {len(ocr_results[0])} 个文本行")

                valid_blocks = 0
                for i, line in enumerate(ocr_results[0]):
                    try:
                        if len(line) >= 2:
                            bbox = line[0]
                            text_info = line[1]

                            if isinstance(text_info, (list, tuple)) and len(text_info) >= 2:
                                text = text_info[0]
                                confidence = text_info[1]

                                # 使用更低的置信度阈值
                                if confidence > 0.05 and text.strip():
                                    bbox_flat = [coord for point in bbox for coord in point]
                                    result.add_text_block(text, bbox_flat, confidence)
                                    valid_blocks += 1

                                    if i < 3:
                                        self.logger.debug(f"重试文本块{i}: '{text}' (置信度: {confidence:.3f})")
                    except Exception as e:
                        self.logger.warning(f"重试解析文本行{i}失败: {e}")
                        continue

                self.logger.info(f"重试成功解析 {valid_blocks} 个有效文本块")

            return result

        except Exception as e:
            self.logger.error(f"重试OCR识别失败: {e}")
            return result
    
    def _process_table_ocr(self, image: np.ndarray, result: OCRResult) -> OCRResult:
        """处理表格OCR - 优先使用PP-DocTranslation，回退到PPStructure"""
        try:
            # 优先使用PP-DocTranslation
            if self.pp_doc_translation:
                return self._process_doc_translation_ocr(image, result)

            # 回退到传统PPStructure
            if self.ocr_table:
                # 使用PPStructure进行表格识别
                structure_results = self.ocr_table(image)

                for item in structure_results:
                    if item['type'] == 'table':
                        # 表格数据
                        table_data = {
                            'type': 'table',
                            'bbox': item['bbox'],
                            'html': item.get('res', {}).get('html', ''),
                            'confidence': item.get('confidence', 0.0)
                        }
                        result.add_table(table_data)
                    elif item['type'] == 'text':
                        # 文本数据
                        text = item.get('res', '')
                        bbox = item.get('bbox', [])
                        confidence = item.get('confidence', 0.0)

                        result.add_text_block(text, bbox, confidence)
            else:
                # 回退到通用OCR
                result = self._process_general_ocr(image, result)

            return result

        except Exception as e:
            self.logger.error(f"表格OCR处理失败: {e}")
            # 回退到通用OCR
            return self._process_general_ocr(image, result)

    def _process_structure_v3_ocr(self, image: Union[np.ndarray, str, Path], result: OCRResult) -> OCRResult:
        """处理PP-StructureV3 OCR"""
        try:
            if not self.pp_structure_v3:
                self.logger.warning("PP-StructureV3未初始化，回退到通用OCR")
                return self._process_general_ocr(image, result)

            self.logger.debug("开始执行PP-StructureV3识别...")

            # 执行PP-StructureV3识别
            structure_results = self.pp_structure_v3.predict(image)

            self.logger.debug(f"PP-StructureV3结果类型: {type(structure_results)}")

            if not structure_results:
                self.logger.warning("PP-StructureV3返回空结果")
                return result

            # 处理结果
            for page_result in structure_results:
                # 处理解析结果列表
                if 'parsing_res_list' in page_result:
                    for block in page_result['parsing_res_list']:
                        block_type = block.get('block_label', 'text')
                        block_content = block.get('block_content', '')
                        block_bbox = block.get('block_bbox', [])

                        if block_type == 'table' and 'table_res_list' in page_result:
                            # 处理表格
                            for table_res in page_result['table_res_list']:
                                table_data = {
                                    'type': 'table',
                                    'bbox': block_bbox,
                                    'html': table_res.get('pred_html', ''),
                                    'confidence': 1.0  # PP-StructureV3不直接提供置信度
                                }
                                result.add_table(table_data)
                        else:
                            # 处理文本块
                            if block_content.strip():
                                result.add_text_block(
                                    text=block_content,
                                    bbox=block_bbox if isinstance(block_bbox, list) else [],
                                    confidence=1.0
                                )

                # 处理全局OCR结果
                if 'overall_ocr_res' in page_result:
                    ocr_res = page_result['overall_ocr_res']
                    if 'rec_texts' in ocr_res and 'rec_polys' in ocr_res:
                        texts = ocr_res['rec_texts']
                        polys = ocr_res['rec_polys']
                        scores = ocr_res.get('rec_scores', [1.0] * len(texts))

                        for text, poly, score in zip(texts, polys, scores):
                            if text.strip():
                                # 将多边形转换为边界框
                                if isinstance(poly, np.ndarray) and poly.shape == (4, 2):
                                    bbox = [
                                        float(poly[:, 0].min()),  # x_min
                                        float(poly[:, 1].min()),  # y_min
                                        float(poly[:, 0].max()),  # x_max
                                        float(poly[:, 1].max())   # y_max
                                    ]
                                else:
                                    bbox = []

                                result.add_text_block(text, bbox, float(score))

            self.logger.info(f"PP-StructureV3成功处理，识别到 {len(result.text_blocks)} 个文本块，{len(result.tables)} 个表格")
            return result

        except Exception as e:
            self.logger.error(f"PP-StructureV3处理失败: {e}")
            # 回退到通用OCR
            return self._process_general_ocr(image, result)

    def _process_doc_translation_ocr(self, image: Union[np.ndarray, str, Path], result: OCRResult) -> OCRResult:
        """处理PP-DocTranslation OCR - 仅使用视觉识别功能，不进行翻译"""
        try:
            if not self.pp_doc_translation:
                self.logger.warning("PP-DocTranslation未初始化，回退到通用OCR")
                return self._process_general_ocr(image, result)

            self.logger.debug("开始执行PP-DocTranslation识别...")

            # 执行PP-DocTranslation视觉预测（不进行翻译，chat_bot_config=None）
            visual_results = self.pp_doc_translation.visual_predict(image)

            self.logger.debug(f"PP-DocTranslation结果类型: {type(visual_results)}")

            if not visual_results:
                self.logger.warning("PP-DocTranslation返回空结果")
                return result

            # 处理结果
            for page_result in visual_results:
                layout_parsing_result = page_result.get("layout_parsing_result")
                if not layout_parsing_result:
                    continue

                # 获取结果数据
                res_data = layout_parsing_result.res if hasattr(layout_parsing_result, 'res') else {}

                # 处理解析结果列表
                parsing_res_list = res_data.get('parsing_res_list', [])
                for block in parsing_res_list:
                    block_type = block.get('block_label', 'text')
                    block_content = block.get('block_content', '')
                    block_bbox = block.get('block_bbox', [])

                    if block_type == 'table':
                        # 处理表格
                        table_data = {
                            'type': 'table',
                            'bbox': block_bbox,
                            'html': block_content,  # PP-DocTranslation直接提供HTML内容
                            'confidence': 1.0
                        }
                        result.add_table(table_data)
                    else:
                        # 处理文本块
                        if block_content.strip():
                            result.add_text_block(
                                text=block_content,
                                bbox=block_bbox if isinstance(block_bbox, list) else [],
                                confidence=1.0
                            )

                # 处理全局OCR结果
                overall_ocr_res = res_data.get('overall_ocr_res', {})
                if 'rec_texts' in overall_ocr_res and 'rec_polys' in overall_ocr_res:
                    texts = overall_ocr_res['rec_texts']
                    polys = overall_ocr_res['rec_polys']
                    scores = overall_ocr_res.get('rec_scores', [1.0] * len(texts))

                    for text, poly, score in zip(texts, polys, scores):
                        if text.strip():
                            # 将多边形转换为边界框
                            if isinstance(poly, np.ndarray) and poly.shape == (4, 2):
                                bbox = [
                                    float(poly[:, 0].min()),  # x_min
                                    float(poly[:, 1].min()),  # y_min
                                    float(poly[:, 0].max()),  # x_max
                                    float(poly[:, 1].max())   # y_max
                                ]
                            else:
                                bbox = []

                            result.add_text_block(text, bbox, float(score))

            self.logger.info(f"PP-DocTranslation成功处理，识别到 {len(result.text_blocks)} 个文本块，{len(result.tables)} 个表格")
            return result

        except Exception as e:
            self.logger.error(f"PP-DocTranslation处理失败: {e}")
            # 回退到通用OCR
            return self._process_general_ocr(image, result)

    def process_multiple_images(self, images: List[Union[np.ndarray, Image.Image, str, Path]],
                              progress_callback: Optional[Callable] = None) -> List[OCRResult]:
        """
        处理多张图像
        
        Args:
            images: 图像列表
            progress_callback: 进度回调函数
            
        Returns:
            OCR结果列表
        """
        results = []
        total_images = len(images)
        
        for i, image in enumerate(images):
            try:
                # 更新进度
                progress = int((i / total_images) * 100)
                status = f"正在处理第 {i+1}/{total_images} 页"
                if self.progress_callback:
                    self.progress_callback(progress, status)
                
                if progress_callback:
                    progress_callback(progress, status)
                
                # 处理图像
                result = self.process_image(image, page_number=i+1)
                results.append(result)
                
            except Exception as e:
                self.logger.error(f"处理第 {i+1} 页失败: {e}")
                # 创建空结果
                empty_result = OCRResult()
                empty_result.page_number = i+1
                results.append(empty_result)
        
        # 完成进度
        self.progress_updated.emit(100, f"处理完成，共 {total_images} 页")
        
        return results
    
    def cleanup(self):
        """清理资源"""
        self.ocr_general = None
        self.ocr_table = None
        self.ocr_layout = None
        self.pp_structure_v3 = None
        self.pp_doc_translation = None
        self.is_initialized = False
